<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSC Conquest HQ | 2026 Ultimate Guide</title>
    <style>
        /* --- Google Fonts --- */
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&family=Hind+Siliguri:wght@400;500;600;700&display=swap');

        /* --- Global Styles & Dark Theme --- */
        :root {
            --bg-dark: #101010;
            --primary-dark: #1c1c1c;
            --secondary-dark: #2d2d2d;
            --text-primary: #f0f0f0;
            --text-secondary: #a0a0a0;
            --accent-primary: #00aaff;
            --accent-secondary: #0077b6;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { scroll-behavior: smooth; }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.8;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { font-family: 'Roboto', sans-serif; font-weight: 700; color: var(--accent-primary); }

        /* --- Header & Navigation --- */
        .header {
            background-color: rgba(28, 28, 28, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid var(--secondary-dark);
        }
        .navbar { display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .logo { font-size: 1.6rem; font-weight: 700; color: var(--text-primary); }
        .logo span { color: var(--accent-primary); }
        .nav-links { list-style: none; display: flex; }
        .nav-links li { margin-left: 35px; }
        .nav-links a { text-decoration: none; color: var(--text-primary); font-weight: 500; transition: color 0.3s ease; }
        .nav-links a:hover { color: var(--accent-primary); }

        /* --- Hero Section --- */
        .hero {
            height: 100vh;
            display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center;
            background: linear-gradient(rgba(18, 18, 18, 0.8), rgba(18, 18, 18, 1)), url('https://images.unsplash.com/photo-1519681393784-d120267933ba?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80') no-repeat center center/cover;
            padding: 0 20px;
        }
        .hero h1 { font-size: 3.2rem; }
        .hero p { font-size: 1.2rem; max-width: 700px; margin: 15px auto 30px auto; color: var(--text-secondary); }
        #countdown { display: flex; justify-content: center; gap: 20px; }
        .time-box { background: var(--secondary-dark); padding: 20px; border-radius: 10px; min-width: 110px; box-shadow: 0 5px 20px rgba(0,0,0,0.5); transition: transform 0.3s; }
        .time-box:hover { transform: translateY(-5px); }
        .time-box span { display: block; font-size: 3rem; font-weight: 700; }
        .time-box p { font-size: 1rem; margin: 0; color: var(--text-secondary); }

        /* --- Section Styling --- */
        .section { padding: 100px 0; border-bottom: 1px solid var(--secondary-dark); }
        .section-title { text-align: center; font-size: 2.8rem; margin-bottom: 60px; }

        /* --- Tab System --- */
        .tab-navigation { display: flex; justify-content: center; flex-wrap: wrap; gap: 10px; margin-bottom: 40px; }
        .tab-button {
            padding: 12px 25px; font-size: 1rem; font-family: 'Hind Siliguri', sans-serif; font-weight: 600;
            background: var(--secondary-dark); color: var(--text-secondary);
            border: none; border-radius: 50px; cursor: pointer; transition: all 0.3s ease;
        }
        .tab-button:hover { background: var(--accent-secondary); color: var(--text-primary); }
        .tab-button.active { background: var(--accent-primary); color: #fff; box-shadow: 0 0 20px rgba(0, 170, 255, 0.4); }
        .tab-pane { display: none; animation: fadeIn 0.6s ease-in-out; }
        .tab-pane.active { display: block; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        
        .table-container { overflow-x: auto; background: var(--primary-dark); padding: 20px; border-radius: 10px; }
        table { width: 100%; border-collapse: collapse; min-width: 800px; }
        th, td { padding: 18px; text-align: left; border-bottom: 1px solid var(--secondary-dark); }
        th { background-color: var(--accent-primary); color: var(--bg-dark); font-weight: 700; }
        tbody tr:nth-child(even) { background-color: var(--secondary-dark); }
        tbody tr:hover { background-color: var(--accent-secondary); transition: background-color 0.2s; }
        td:first-child { font-weight: 600; }

        /* --- Pro Tips Section --- */
        .tips-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; }
        .tip-card { background: var(--primary-dark); padding: 30px; border-radius: 10px; border-left: 5px solid var(--accent-primary); transition: transform 0.3s, box-shadow 0.3s; }
        .tip-card:hover { transform: translateY(-10px); box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4); }
        
        /* --- Footer --- */
        .footer { text-align: center; padding: 40px 20px; background-color: var(--primary-dark); }
        
        /* --- Responsive Design --- */
        @media (max-width: 768px) {
            .hero h1 { font-size: 2.5rem; }
            .nav-links { display: none; }
        }
    </style>
</head>
<body>

    <header class="header">
        <nav class="navbar">
            <a href="#" class="logo">SSC <span>HQ</span></a>
            <ul class="nav-links">
                <li><a href="#home">হোম</a></li>
                <li><a href="#full-plan">স্টাডি প্ল্যান</a></li>
                <li><a href="#pro-tips">সেরা কৌশল</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="home" class="hero">
            <h1>SSC Conquest HQ 2026</h1>
            <p>প্রাক-নির্বাচনী পরীক্ষার বাকি আর মাত্র...</p>
            <div id="countdown">
                <div class="time-box"> <span id="days">00</span> <p>দিন</p> </div>
                <div class="time-box"> <span id="hours">00</span> <p>ঘন্টা</p> </div>
                <div class="time-box"> <span id="minutes">00</span> <p>মিনিট</p> </div>
                <div class="time-box"> <span id="seconds">00</span> <p>সেকেন্ড</p> </div>
            </div>
        </section>

        <section id="full-plan" class="section">
            <div class="container">
                <h2 class="section-title">পূর্ণাঙ্গ স্টাডি প্ল্যান</h2>
                <div class="tab-navigation">
                    <button class="tab-button active" data-tab="physics">🌌 পদার্থবিজ্ঞান</button>
                    <button class="tab-button" data-tab="chemistry">🧪 রসায়ন</button>
                    <button class="tab-button" data-tab="biology">🧬 জীববিজ্ঞান</button>
                    <button class="tab-button" data-tab="math">🧮 সাধারণ গণিত</button>
                    <button class="tab-button" data-tab="h-math">📐 উচ্চতর গণিত</button>
                    <button class="tab-button" data-tab="bangla">🖋️ বাংলা</button>
                    <button class="tab-button" data-tab="english">🇬🇧 English</button>
                    <button class="tab-button" data-tab="bgs">🇧🇩 BGS</button>
                    <button class="tab-button" data-tab="ict">🖥️ ICT</button>
                    <button class="tab-button" data-tab="religion">🙏 ধর্ম</button>
                </div>

                <div class="tab-content">
                    <!-- Physics Pane -->
                    <div id="physics" class="tab-pane active"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>কঠিন টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>১</td><td>ভৌত রাশি ও পরিমাপ</td><td>ভার্নিয়ার স্কেল, স্ক্রু গজ</td><td>যন্ত্রের অ্যানিমেটেড ভিডিও দেখা, অঙ্ক অনুশীলন।</td></tr>
                        <tr><td>২</td><td>গতি</td><td>গতির সমীকরণ, লেখচিত্র, নিক্ষিপ্ত বস্তু</td><td>প্রতিদিন ৫টি সৃজনশীল সমাধান, গ্রাফ হাতে আঁকা।</td></tr>
                        <tr><td>৩</td><td>বল</td><td>ভরবেগের সংরক্ষণশীলতা, ঘর্ষণ</td><td>বাস্তব উদাহরণ দিয়ে সূত্র বোঝা (নৌকার লাফ)।</td></tr>
                        <tr><td>৪</td><td>কাজ, ক্ষমতা ও শক্তি</td><td>কর্মদক্ষতা, কূপের অঙ্ক</td><td>কর্মদক্ষতার সকল সূত্র একটি চার্টে লেখা।</td></tr>
                        <tr><td>৫</td><td>পদার্থের অবস্থা ও চাপ</td><td>আর্কিমিডিসের সূত্র, প্যাসকেলের নীতি</td><td>চিত্র এঁকে প্লবতা ও চাপ বোঝা।</td></tr>
                        <tr><td>৭</td><td>তরঙ্গ ও শব্দ</td><td>প্রতিধ্বনি ব্যবহার করে দূরত্ব নির্ণয়</td><td>প্রতিধ্বনির সব ধরনের (বাদুড়, কূপ) অঙ্ক করা।</td></tr>
                        <tr><td>৮</td><td>আলোর প্রতিফলন</td><td>অবতল ও উত্তল দর্পণে বিম্ব আঁকা</td><td>রশ্মিচিত্র আঁকা বাধ্যতামূলক, না এঁকলে হবে না।</td></tr>
                        <tr><td>১১</td><td>চল তড়িৎ</td><td>জটিল বর্তনীর তুল্য রোধ, বিদ্যুৎ বিল</td><td>জটিল সার্কিটকে সহজ করে আঁকার কৌশল শেখা।</td></tr>
                    </tbody></table></div></div>
                    
                    <!-- Chemistry Pane -->
                    <div id="chemistry" class="tab-pane"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>কঠিন টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>২</td><td>পদার্থের অবস্থা</td><td>ব্যাপন ও নিঃসরণের হার</td><td>বাস্তব উদাহরণ (সেন্টের গন্ধ) দিয়ে বোঝা।</td></tr>
                        <tr><td>৩</td><td>পদার্থের গঠন</td><td>আইসোটোপ, আপেক্ষিক পারমাণবিক ভর</td><td>প্রথম ৩৬টি মৌলের ইলেক্ট্রন বিন্যাস শেখা।</td></tr>
                        <tr><td>৪</td><td>পর্যায় সারণি</td><td>পর্যায়বৃত্ত ধর্ম (আকার, আয়নীকরণ শক্তি)</td><td>ছক ও তীর চিহ্ন দিয়ে নোট করা, ব্যতিক্রম মার্ক করা।</td></tr>
                        <tr><td>৫</td><td>রাসায়নিক বন্ধন</td><td>আয়নিক ও সমযোজী যৌগের বৈশিষ্ট্য</td><td>বিভিন্ন যৌগের (পানি, লবণ) বন্ধন গঠন চিত্র আঁকা।</td></tr>
                        <tr><td>৬</td><td>মোলের ধারণা</td><td>লিমিটিং বিক্রিয়কের অঙ্ক, শতকরা সংযুতি</td><td>এটি পুরোপুরি অনুশীলনের অধ্যায়, যত বেশি তত ভালো।</td></tr>
                        <tr><td>৭</td><td>রাসায়নিক বিক্রিয়া</td><td>জারণ-বিজারণ, লা-শাতেলিয়ার নীতি</td><td>জারণ সংখ্যা নির্ণয়ের নিয়ম শেখা, বিক্রিয়া না দেখে লেখা।</td></tr>
                        <tr><td>১১</td><td>খনিজ সম্পদ: জীবাশ্ম</td><td>অ্যালকেনের রূপান্তর, পলিমারকরণ</td><td>বিক্রিয়াগুলোকে ফ্লো-চার্টে (অ্যালকেন ➡️ অ্যালকিন) সাজানো।</td></tr>
                    </tbody></table></div></div>

                    <!-- Biology Pane -->
                    <div id="biology" class="tab-pane"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>কঠিন টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>১</td><td>জীবন পাঠ</td><td>জীবজগতের শ্রেণিবিন্যাস</td><td>বড় কাগজে ছক এঁকে টেবিলে লাগানো।</td></tr>
                        <tr><td>২</td><td>জীবকোষ ও টিস্যু</td><td>কোষীয় অঙ্গাণুর গঠন (মাইটোকন্ড্রিয়া)</td><td>রঙিন কলম দিয়ে চিত্র এঁকে লেবেলিং করে পড়া।</td></tr>
                        <tr><td>৪</td><td>জীবনীশক্তি</td><td>সালোকসংশ্লেষণ ও শ্বসনের ধাপ</td><td>ফ্লো-চার্টে এঁকে ATP-র হিসাব বোঝা।</td></tr>
                        <tr><td>৫</td><td>খাদ্য, পুষ্টি এবং পরিপাক</td><td>বিভিন্ন এনজাইমের উৎস ও কাজ</td><td>মানবদেহের ছবি এঁকে পরিপাকতন্ত্র চিহ্নিত করা।</td></tr>
                        <tr><td>৬</td><td>জীবে পরিবহন</td><td>হৃদপিণ্ডের গঠন ও রক্ত সঞ্চালন</td><td>হৃদপিণ্ডের বড় চিত্র এঁকে রক্ত প্রবাহের পথ দেখানো।</td></tr>
                        <tr><td>৮</td><td>মানব রেচন</td><td>নেফ্রনের গঠন ও মূত্র তৈরির প্রক্রিয়া</td><td>নেফ্রনের চিত্রটি খুবই গুরুত্বপূর্ণ, এঁকে অনুশীলন করা।</td></tr>
                        <tr><td>১১</td><td>জীবের প্রজনন</td><td>ফুলের লম্বচ্ছেদ, দ্বি-নিষেক প্রক্রিয়া</td><td>ফুলের চিত্র ও দ্বি-নিষেক প্রক্রিয়া ধাপে ধাপে আঁকা।</td></tr>
                        <tr><td>১২</td><td>জীবের বংশগতি ও বিবর্তন</td><td>DNA-এর গঠন, মেন্ডেলের সূত্র</td><td>চেকার বোর্ডে বিভিন্ন বৈশিষ্ট্য নিয়ে অনুশীলন করা।</td></tr>
                    </tbody></table></div></div>
                    
                    <!-- General Math Pane -->
                    <div id="math" class="tab-pane"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>কঠিন টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>২</td><td>সেট ও ফাংশন</td><td>ফাংশনের ডোমেন-রেঞ্জ, অন্বয়</td><td>শর্তগুলো ভালোভাবে বুঝে অনুশীলন করা।</td></tr>
                        <tr><td>৩</td><td>বীজগাণিতিক রাশি</td><td>ঘন ও বর্গের সূত্রের জটিল প্রয়োগ</td><td>প্রতিটি নিয়মের অঙ্ক বারবার করা।</td></tr>
                        <tr><td>৭</td><td>ব্যবহারিক জ্যামিতি</td><td>বৃত্তের স্পর্শক সংক্রান্ত সম্পাদ্য</td><td>সঠিক যন্ত্রপাতি ব্যবহার করে বারবার আঁকা।</td></tr>
                        <tr><td>৮</td><td>বৃত্ত</td><td>বৃত্তের জ্যা, চাপ, স্পর্শক সংক্রান্ত উপপাদ্য</td><td>উপপাদ্য না দেখে লেখার অভ্যাস করা।</td></tr>
                        <tr><td>৯</td><td>ত্রিকোণমিতি</td><td>উচ্চতা ও দূরত্ব বিষয়ক সৃজনশীল</td><td>চিত্র এঁকে সমস্যা সমাধানের চেষ্টা করা।</td></tr>
                        <tr><td>১৩</td><td>সসীম ধারা</td><td>গুণোত্তর ধারার সমষ্টি ও পদ নির্ণয়</td><td>সমান্তর ও গুণোত্তর ধারার সূত্র গুলিয়ে না ফেলা।</td></tr>
                        <tr><td>১৬</td><td>পরিমিতি</td><td>সুষম ও যৌগিক ঘনবস্তুর পৃষ্ঠতলের ক্ষেত্রফল ও আয়তন</td><td>সূত্রগুলো একটি চার্টে লিখে রাখা।</td></tr>
                        <tr><td>১৭</td><td>পরিসংখ্যান</td><td>সংক্ষিপ্ত পদ্ধতিতে গড়, অজিভ রেখা</td><td>ডেটা টেবিল নির্ভুলভাবে তৈরি করা।</td></tr>
                    </tbody></table></div></div>

                    <!-- Higher Math Pane -->
                    <div id="h-math" class="tab-pane"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>কঠিন টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>২</td><td>বীজগাণিতিক রাশি</td><td>আংশিক ভগ্নাংশ</td><td>নিয়ম অনুযায়ী অনুশীলন করা।</td></tr>
                        <tr><td>৩</td><td>জ্যামিতি</td><td>পিথাগোরাসের বিস্তৃতি, অ্যাপোলোনিয়াসের উপপাদ্য</td><td>উপপাদ্যগুলো ধাপে ধাপে বুঝে লেখা।</td></tr>
                        <tr><td>৮</td><td>ত্রিকোণমিতি</td><td>জটিল সূত্রের প্রমাণ ও প্রয়োগ</td><td>সকল সূত্র একটি কাগজে লিখে প্রতিদিন দেখা।</td></tr>
                        <tr><td>৯</td><td>সূচকীয় ও লগারিদমীয় ফাংশন</td><td>লগারিদমের জটিল অঙ্ক, লেখচিত্র</td><td>লগ ও সূচকের নিয়মাবলী ভালোভাবে আয়ত্ত করা।</td></tr>
                        <tr><td>১০</td><td>দ্বিপদী বিস্তৃতি</td><td>x-মুক্ত পদ, মধ্যপদ নির্ণয়</td><td>প্যাসকেলের ত্রিভুজ ও দ্বিপদী উপপাদ্য অনুশীলন।</td></tr>
                        <tr><td>১১</td><td>স্থানাঙ্ক জ্যামিতি</td><td>সরলরেখার সমীকরণ সংক্রান্ত সমস্যা</td><td>গ্রাফ পেপারে চিত্র এঁকে অঙ্ক করা।</td></tr>
                    </tbody></table></div></div>

                    <!-- Bangla Pane -->
                    <div id="bangla" class="tab-pane"><div class="table-container"><table><thead><tr><th>পত্র</th><th>বিভাগ</th><th>গুরুত্বপূর্ণ টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>১ম</td><td>গদ্য</td><td>শিক্ষা ও মনুষ্যত্ব, বই পড়া, দেনাপাওনা</td><td>মূলভাব ও চরিত্র বিশ্লেষণ করে নোট করা।</td></tr>
                        <tr><td>১ম</td><td>পদ্য</td><td>কপোতাক্ষ নদ, মানুষ, আমার পরিচয়</td><td>কবিতার কাব্যিক সৌন্দর্য ও অন্তর্নিহিত অর্থ বোঝা।</td></tr>
                        <tr><td>১ম</td><td>সহপাঠ</td><td>কাক্তাড়ুয়া / বহিপীর</td><td>উপন্যাস/নাটকের ঘটনাপ্রবাহের টাইমলাইন বানানো।</td></tr>
                        <tr><td>২য়</td><td>ব্যাকরণ</td><td>সমাস, কারক, উপসর্গ, প্রকৃতি ও প্রত্যয়</td><td>প্রতিদিন একটি টপিক নিয়ে বোর্ডের প্রশ্ন সমাধান।</td></tr>
                        <tr><td>২য়</td><td>নির্মিতি</td><td>প্রতিবেদন, ভাব-সম্প্রসারণ, সারাংশ</td><td>সঠিক ফরম্যাট অনুসরণ করে ঘড়ি ধরে লেখা।</td></tr>
                    </tbody></table></div></div>
                    
                    <!-- English Pane -->
                    <div id="english" class="tab-pane"><div class="table-container"><table><thead><tr><th>Paper</th><th>Section</th><th>Key Topics</th><th>Conquest Strategy</th></tr></thead><tbody>
                        <tr><td>1st</td><td>Reading</td><td>MCQ, Answering Questions, Summarizing</td><td>Daily reading practice, learn 5 new vocabularies.</td></tr>
                        <tr><td>1st</td><td>Writing</td><td>Paragraph, Completing Story, Email</td><td>Practice writing on different topics with proper format.</td></tr>
                        <tr><td>2nd</td><td>Grammar</td><td>Right form of verbs, Connectors, Narration</td><td>Master one grammar rule at a time and solve board questions.</td></tr>
                        <tr><td>2nd</td><td>Composition</td><td>CV with Cover Letter, Formal Letter</td><td>Learn the correct format and practice writing.</td></tr>
                    </tbody></table></div></div>

                    <!-- BGS Pane -->
                    <div id="bgs" class="tab-pane"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>গুরুত্বপূর্ণ টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>১</td><td>পূর্ব বাংলার আন্দোলন ও জাতীয়তাবাদ</td><td>ভাষা আন্দোলন, ৬-দফা, '৬৯ এর গণঅভ্যুত্থান</td><td>ঘটনাগুলো সাল অনুযায়ী টাইমলাইন করে পড়া।</td></tr>
                        <tr><td>২</td><td>স্বাধীন বাংলাদেশ</td><td>মুক্তিযুদ্ধের পটভূমি, মুজিবনগর সরকার</td><td>মানচিত্র দেখে মুক্তিযুদ্ধের সেক্টরগুলো চেনা।</td></tr>
                        <tr><td>৪</td><td>বাংলাদেশের ভূ-প্রকৃতি ও জলবায়ু</td><td>ভূ-প্রাকৃতিক অঞ্চলের বৈশিষ্ট্য</td><td>বৈশিষ্ট্যগুলো ছক আকারে তুলনা করে পড়া।</td></tr>
                        <tr><td>৬</td><td>রাষ্ট্র, নাগরিকতা ও আইন</td><td>সরকারের বিভিন্ন অঙ্গ ও তাদের কাজ</td><td>ফ্লো-চার্টের মাধ্যমে সরকার ব্যবস্থা বোঝা।</td></tr>
                        <tr><td>১১</td><td>জাতীয় সম্পদ ও অর্থনৈতিক ব্যবস্থা</td><td>জাতীয় সম্পদের শ্রেণিবিভাগ</td><td>বাস্তব উদাহরণ দিয়ে সম্পদের ধারণা পরিষ্কার করা।</td></tr>
                    </tbody></table></div></div>
                    
                    <!-- ICT Pane -->
                    <div id="ict" class="tab-pane"><div class="table-container"><table><thead><tr><th>অধ্যায়</th><th>নাম</th><th>গুরুত্বপূর্ণ টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>১</td><td>তথ্য ও যোগাযোগ প্রযুক্তি এবং আমাদের বাংলাদেশ</td><td>ই-লার্নিং, ই-গভর্ন্যান্স, ই-কমার্স</td><td>বাস্তব জীবনের ব্যবহারের সাথে মিলিয়ে পড়া।</td></tr>
                        <tr><td>২</td><td>কম্পিউটার ও কম্পিউটার ব্যবহারকারীর নিরাপত্তা</td><td>কম্পিউটার নেটওয়ার্ক, সাইবার ক্রাইম</td><td>বিভিন্ন প্রকার নেটওয়ার্কের (LAN, MAN, WAN) পার্থক্য বোঝা।</td></tr>
                        <tr><td>৩</td><td>আমার শিক্ষায় ইন্টারনেট</td><td>ডিজিটাল কনটেন্ট, শিক্ষামূলক ওয়েবসাইট</td><td>নিজে কিছু শিক্ষামূলক ওয়েবসাইট ভিজিট করা।</td></tr>
                        <tr><td>৪</td><td>আমার লেখালেখি ও হিসাব</td><td>ওয়ার্ড প্রসেসিং, স্প্রেডশিট</td><td>কম্পিউটারে এক্সেল খুলে সূত্র ব্যবহার করে দেখা।</td></tr>
                        <tr><td>৫</td><td>মাল্টিমিডিয়া ও গ্রাফিক্স</td><td>পাওয়ারপয়েন্ট প্রেজেন্টেশন, ফটোশপের ধারণা</td><td>নিজে একটি প্রেজেন্টেশন তৈরি করার চেষ্টা করা।</td></tr>
                    </tbody></table></div></div>
                    
                    <!-- Religion Pane -->
                    <div id="religion" class="tab-pane"><div class="table-container"><table><thead><tr><th>ধর্ম</th><th>বিভাগ</th><th>গুরুত্বপূর্ণ টপিক</th><th>জয়ের কৌশল</th></tr></thead><tbody>
                        <tr><td>ইসলাম</td><td>আকাইদ ও নৈতিক জীবন</td><td>ঈমান, শিরক, তাকওয়া</td><td>নৈতিক শিক্ষাগুলো নিজের জীবনে ধারণ করা।</td></tr>
                        <tr><td>ইসলাম</td><td>ইবাদত</td><td>সালাত, সাওম, যাকাত, হজ্জ</td><td>ইবাদতের নিয়ম ও তাৎপর্য বোঝা।</td></tr>
                        <tr><td>ইসলাম</td><td>কুরআন ও হাদিস শিক্ষা</td><td>সূরা মাউন, সূরা ফাতেহা; পরোপকার সংক্রান্ত হাদিস</td><td>অর্থ ও ব্যাখ্যাসহ পাঠ করা।</td></tr>
                        <tr><td>হিন্দু</td><td>সৃষ্টিকর্তা ও সৃষ্টি</td><td>ব্রহ্ম, ঈশ্বর, দেবতা</td><td>বিভিন্ন দেব-দেবীর পরিচয় ও তাৎপর্য জানা।</td></tr>
                        <tr><td>হিন্দু</td><td>ধর্মগ্রন্থ ও নৈতিক শিক্ষা</td><td>বেদ, উপনিষদ, শ্রীমদ্ভগবদ্গীতা</td><td>গীতার শ্লোক ও তার অর্থ বোঝার চেষ্টা করা।</td></tr>
                    </tbody></table></div></div>
                </div>
            </div>
        </section>

        <section id="pro-tips" class="section">
            <div class="container">
                <h2 class="section-title">সেরা কৌশল ও রণনীতি</h2>
                <div class="tips-grid">
                    <div class="tip-card"><h3>পোমোডোরো টেকনিক</h3><p>২৫ মিনিট পড়ুন, ৫ মিনিট বিরতি নিন। মনোযোগের জন্য সেরা।</p></div>
                    <div class="tip-card"><h3>অ্যাক্টিভ রিকল</h3><p>বই বন্ধ করে পড়া বিষয়টি মনে করার চেষ্টা করুন। স্মৃতিতে স্থায়ী হয়।</p></div>
                    <div class="tip-card"><h3>মাইন্ড ম্যাপিং</h3><p>বড় অধ্যায়কে একটি ডায়াগ্রামের মাধ্যমে সাজিয়ে ফেলুন। রিভিশনের জন্য সেরা।</p></div>
                    <div class="tip-card"><h3>স্বাস্থ্যই সম্পদ</h3><p>পর্যাপ্ত ঘুম (৭-৮ ঘণ্টা) ও স্বাস্থ্যকর খাবার ব্রেইনের ক্ষমতা বাড়ায়।</p></div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <p>"Your future is created by what you do today, not tomorrow."</p>
        <p>© 2024 SSC Conquest HQ. All Rights Reserved.</p>
    </footer>

    <script>
        // --- Countdown Timer Logic ---
        const countdown = () => {
            const countDate = new Date("September 1, 2025 00:00:00").getTime();
            const now = new Date().getTime();
            const gap = countDate - now;
            const second = 1000, minute = second * 60, hour = minute * 60, day = hour * 24;
            const textDay = Math.floor(gap / day), textHour = Math.floor((gap % day) / hour), textMinute = Math.floor((gap % hour) / minute), textSecond = Math.floor((gap % minute) / second);
            document.getElementById('days').innerText = textDay < 10 ? '0' + textDay : textDay;
            document.getElementById('hours').innerText = textHour < 10 ? '0' + textHour : textHour;
            document.getElementById('minutes').innerText = textMinute < 10 ? '0' + textMinute : textMinute;
            document.getElementById('seconds').innerText = textSecond < 10 ? '0' + textSecond : textSecond;
        };
        setInterval(countdown, 1000);

        // --- Tab System Logic ---
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                tabPanes.forEach(pane => {
                    if (pane.id === targetTab) { pane.classList.add('active'); } 
                    else { pane.classList.remove('active'); }
                });
            });
        });
    </script>
</body>
</html>